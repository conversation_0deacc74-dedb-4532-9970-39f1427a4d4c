import os
import subprocess
import time
import logging
from pathlib import Path
import cv2

# Import des utilitaires
from utils.visualisation import reconvert_predictions
from utils.conversion import convert_images_to_grayscale
from utils.rename import rename_imagesTr, initial_rename
from utils.overlay_manager import OverlayManager

# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "029"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné
GPU_ID = "0"              # "0" ou "0,1,2,3" pour multi-GPU

# === DOSSIERS D'INFÉRENCE ===
INPUT_FOLDER = "/home/<USER>/localfiles/gabriel.forest/MX2-757-SC-PS-00575-Z-12000 IA Training/Gr01/base/no_label/endviews_uint8/complete"     # MODIFIEZ : Dossier contenant les images à prédire
BASE_OUTPUT_FOLDER = "/mnt/results/inference"  # MODIFIEZ : Dossier de base pour les résultats

# === PATHS ===
RAW_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw"
PREPROCESSED_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\nnUnet\nnUNet_preprocessed"
RESULTS_PATH = r"C:\Users\<USER>\Documents\4Corrosion\Results\nnUNet_results"

# # === CHEMINS nnUNet (MÊME CONFIG QUE train_nnunet.py) ===
# RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
# PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
# RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH
os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID

# Fix OpenMP library conflict
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

# === VARIABLE GLOBALE POUR LE DOSSIER DE SORTIE ===
OUTPUT_FOLDER = None  # Sera configuré par setup_output_folder()

# === LOGGING SIMPLE ===
def setup_logging():
    """Configure le logging simple"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"nnunet_inference_{DATASET_ID}_{timestamp}.log"

    # Configure file handler with UTF-8 encoding
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # Configure console handler with UTF-8 encoding and error handling
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Set formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Configure root logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

logger = setup_logging()

def setup_output_folder():
    """Configure le dossier de sortie avec versionnage automatique"""
    global OUTPUT_FOLDER

    # Nom basé sur le dossier d'entrée
    if INPUT_FOLDER != "/path/to/your/input/images":
        input_name = os.path.basename(INPUT_FOLDER.rstrip("\\/")).replace(" ", "_")
        output_candidate = os.path.join(os.path.dirname(BASE_OUTPUT_FOLDER), input_name)
    else:
        output_candidate = BASE_OUTPUT_FOLDER

    # Ajouter suffixe _v2, _v3 si nécessaire
    OUTPUT_FOLDER = output_candidate
    version = 2
    while os.path.exists(OUTPUT_FOLDER):
        OUTPUT_FOLDER = f"{output_candidate}_v{version}"
        version += 1

    # Créer le dossier
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)

    # Afficher le message avec information de versionnage
    if version > 2:
        logger.info(f"[OUTPUT] Dossier de sortie configuré: {OUTPUT_FOLDER} (version {version-1})")
    else:
        logger.info(f"[OUTPUT] Dossier de sortie configuré: {OUTPUT_FOLDER}")

    return OUTPUT_FOLDER

def run_command(cmd: str, description: str = "") -> bool:
    """Exécute une commande avec gestion d'erreurs"""
    logger.info(f"[START] {description if description else 'Lancement'}: {cmd}")

    try:
        subprocess.run(cmd, shell=True, check=True)
        logger.info(f"[SUCCESS] Succès: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"[ERROR] Erreur lors de: {description}")
        logger.error(f"Code de retour: {e.returncode}")
        return False

def find_inference_files():
    """Trouve les fichiers d'inférence générés par train_nnunet.py"""
    import glob
    dataset_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*")
    folders = glob.glob(dataset_folder)

    if not folders:
        logger.error(f"[ERROR] Dossier dataset non trouvé: {dataset_folder}")
        return None, None

    dataset_path = folders[0]
    inference_instructions = os.path.join(dataset_path, "inference_instructions.txt")
    inference_information = os.path.join(dataset_path, "inference_information.json")

    return inference_instructions, inference_information

def check_model_exists() -> bool:
    """Vérifie que le modèle entraîné existe et lit les instructions d'inférence"""
    inference_instructions, _ = find_inference_files()

    if inference_instructions and os.path.exists(inference_instructions):
        logger.info(f"[SUCCESS] Fichier d'instructions trouvé: {inference_instructions}")

        # Lire les instructions d'inférence
        try:
            with open(inference_instructions, 'r') as f:
                instructions = f.read()
            logger.info("[INSTRUCTIONS] Instructions d'inférence disponibles:")
            logger.info(instructions[:200] + "..." if len(instructions) > 200 else instructions)
        except Exception as e:
            logger.warning(f"[WARNING] Impossible de lire les instructions: {e}")

        return True
    else:
        # Fallback: vérification manuelle
        trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
        model_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*",
                                   f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")
        import glob
        folders = glob.glob(model_folder)

        if not folders:
            logger.error(f"[ERROR] Modèle non trouvé: {model_folder}")
            logger.error("[INFO] Assurez-vous d'avoir entraîné le modèle avec train_nnunet.py")
            return False

        logger.info(f"[SUCCESS] Modèle trouvé: {folders[0]}")
        logger.warning("[WARNING] Fichiers d'instructions non trouvés, utilisation de la configuration manuelle")
        return True

def preprocess_input_images() -> bool:
    """Prétraite les images d'entrée (conversion couleur + renommage) - Utilise les fonctions utils"""
    try:
        logger.info("[PREPROCESS] Prétraitement des images d'entrée (format + couleur)...")

        # Conversion couleur → grayscale (non destructif, avec backup)
        logger.info("[CONVERT] Conversion des images en niveaux de gris...")
        convert_images_to_grayscale(INPUT_FOLDER)

        # Première passe de renommage (format xxxx.png)
        logger.info("[RENAME] Première passe de renommage...")
        initial_rename(INPUT_FOLDER)

        # Deuxième passe de renommage (format xxxx_0000.png)
        logger.info("[RENAME] Deuxième passe de renommage (format nnU-Net)...")
        rename_imagesTr(INPUT_FOLDER)

        logger.info("[SUCCESS] Prétraitement des images terminé")
        return True

    except Exception as e:
        logger.error(f"[ERROR] Erreur lors du prétraitement: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def check_input_folder() -> bool:
    """Vérifie que le dossier d'entrée existe et contient des images"""
    if not os.path.exists(INPUT_FOLDER):
        logger.error(f"[ERROR] Dossier d'entrée non trouvé: {INPUT_FOLDER}")
        return False

    # Compter les images compatibles nnUNet
    image_files = [f for f in os.listdir(INPUT_FOLDER)
                   if f.endswith(('.nii.gz', '.nii', '.png', '.tif', '.tiff', '.jpg', '.jpeg'))]

    if not image_files:
        logger.error(f"[ERROR] Aucune image trouvée dans: {INPUT_FOLDER}")
        logger.error("[INFO] Formats supportés: .nii.gz, .nii, .png, .tif, .tiff, .jpg, .jpeg")
        return False

    logger.info(f"[SUCCESS] {len(image_files)} images trouvées dans le dossier d'entrée")
    return True

def get_optimal_inference_command():
    """Récupère la commande d'inférence optimale depuis inference_instructions.txt"""
    inference_instructions, _ = find_inference_files()

    if inference_instructions and os.path.exists(inference_instructions):
        try:
            with open(inference_instructions, 'r') as f:
                content = f.read()

            # Extraire la commande nnUNetv2_predict
            lines = content.split('\n')
            for line in lines:
                if 'nnUNetv2_predict' in line and '-i' in line and '-o' in line:
                    # Remplacer les dossiers par nos dossiers
                    cmd = line.strip()
                    # Remplacer les chemins génériques par nos chemins
                    cmd = cmd.replace('-i INPUT_FOLDER', f'-i "{INPUT_FOLDER}"')
                    cmd = cmd.replace('-o OUTPUT_FOLDER', f'-o "{OUTPUT_FOLDER}"')
                    logger.info("[SUCCESS] Utilisation de la commande optimale depuis inference_instructions.txt")
                    return cmd
        except Exception as e:
            logger.warning(f"[WARNING] Erreur lecture instructions: {e}")

    # Fallback: commande manuelle
    logger.info("[WARNING] Utilisation de la commande manuelle")
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    return f'nnUNetv2_predict -i "{INPUT_FOLDER}" -o "{OUTPUT_FOLDER}" -d {DATASET_ID} -c {CONFIGURATION} -tr {trainer_class}'

def run_inference() -> bool:
    """Exécute l'inférence nnUNet"""
    # Obtenir la commande optimale
    cmd = get_optimal_inference_command()

    success = run_command(cmd, "Inférence nnUNet avec configuration optimale")

    if success:
        logger.info(f"[OUTPUT] Résultats sauvés dans: {OUTPUT_FOLDER}")

        # Lister les fichiers générés
        if os.path.exists(OUTPUT_FOLDER):
            result_files = os.listdir(OUTPUT_FOLDER)
            logger.info(f"[RESULTS] {len(result_files)} fichiers générés:")
            for f in result_files[:5]:  # Afficher les 5 premiers
                logger.info(f"   - {f}")
            if len(result_files) > 5:
                logger.info(f"   ... et {len(result_files) - 5} autres")

        # Appliquer le post-processing nnUNet si nécessaire
        apply_nnunet_postprocessing()

        # Appliquer le post-processing personnalisé (reconversion + overlays)
        run_custom_postprocessing()

    return success

def apply_nnunet_postprocessing():
    """Applique le post-processing nnUNet si nécessaire"""
    inference_instructions, _ = find_inference_files()

    if inference_instructions and os.path.exists(inference_instructions):
        try:
            with open(inference_instructions, 'r') as f:
                content = f.read()

            if 'nnUNetv2_apply_postprocessing' in content:
                logger.info("[POSTPROCESS] Application du post-processing nnUNet...")

                # Créer le dossier de sortie post-processing
                OUTPUT_FOLDER_PP = os.path.join(OUTPUT_FOLDER, "postprocessed")
                os.makedirs(OUTPUT_FOLDER_PP, exist_ok=True)

                lines = content.split('\n')
                for line in lines:
                    if 'nnUNetv2_apply_postprocessing' in line:
                        # Adapter la commande avec nos dossiers
                        cmd = line.strip()
                        cmd = cmd.replace('OUTPUT_FOLDER_PP', f'"{OUTPUT_FOLDER_PP}"')
                        cmd = cmd.replace('OUTPUT_FOLDER', f'"{OUTPUT_FOLDER}"')

                        # Si les chemins INPUT_FOLDER sont présents, les remplacer aussi
                        if 'INPUT_FOLDER' in cmd:
                            cmd = cmd.replace('INPUT_FOLDER', f'"{OUTPUT_FOLDER}"')

                        success = run_command(cmd, "Post-processing nnUNet")
                        if success:
                            logger.info(f"[SUCCESS] Post-processing nnUNet terminé - Résultats dans: {OUTPUT_FOLDER_PP}")
                        return success
        except Exception as e:
            logger.warning(f"[WARNING] Erreur post-processing nnUNet: {e}")

    logger.info("[INFO] Aucun post-processing nnUNet nécessaire")
    return True

def run_custom_postprocessing() -> bool:
    """Exécute le post-processing personnalisé (reconversion + overlays)"""
    try:
        logger.info("[POSTPROCESS] Post-traitement des masques prédits...")

        # Définir les dossiers de sortie
        RECONVERTED_MASKS = os.path.join(OUTPUT_FOLDER, "reconverted_masks")
        OVERLAY_DIR = os.path.join(OUTPUT_FOLDER, "overlays")

        # Reconversion des prédictions
        reconvert_predictions(
            input_dir=OUTPUT_FOLDER,
            output_dir=RECONVERTED_MASKS,
            class_to_value={
                0: 0,    # background
                1: 29,   # frontwall
                2: 149,  # backwall
                3: 76,   # flaw
                4: 125   # indication
            }
        )

        # Création des overlays
        logger.info("[OVERLAY] Création des overlays...")
        os.makedirs(OVERLAY_DIR, exist_ok=True)
        overlay_manager = OverlayManager()

        # Afficher les fichiers disponibles
        logger.info("[FILES] Fichiers dans le dossier d'entrée:")
        for f in os.listdir(INPUT_FOLDER):
            if f.endswith(('.png', '.jpg', '.jpeg')):
                logger.info(f"- {f}")

        logger.info("[FILES] Fichiers dans le dossier des masques:")
        for f in os.listdir(RECONVERTED_MASKS):
            if f.endswith('.png'):
                logger.info(f"- {f}")

        # Traiter chaque image
        for img_name in os.listdir(INPUT_FOLDER):
            if img_name.endswith(('.png', '.jpg', '.jpeg')):
                try:
                    # Charger l'image originale
                    img_path = os.path.join(INPUT_FOLDER, img_name)
                    logger.info(f"Traitement de l'image: {img_name}")

                    original_img = cv2.imread(img_path)
                    if original_img is None:
                        logger.error(f"Impossible de charger l'image: {img_path}")
                        continue

                    # Charger le masque correspondant
                    # Extraire le numéro de l'image (ex: "0001" de "0001_0000.png")
                    img_number = img_name.split('_')[0]
                    mask_name = f"{img_number}.png"  # Format attendu par nnU-Net
                    mask_path = os.path.join(RECONVERTED_MASKS, mask_name)

                    if not os.path.exists(mask_path):
                        logger.error(f"Masque non trouvé: {mask_path}")
                        continue

                    mask_img = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
                    if mask_img is None:
                        logger.error(f"Impossible de charger le masque: {mask_path}")
                        continue

                    logger.info(f"Image originale: {original_img.shape}, Masque: {mask_img.shape}")

                    # Créer l'overlay avec couleurs corrigées (rouge et vert inversés)
                    overlay = overlay_manager.create_high_contrast_overlay(original_img, mask_img, alpha=0.8)

                    # Sauvegarder l'overlay
                    output_path = os.path.join(OVERLAY_DIR, f"overlay_{img_name}")
                    overlay_manager.save_overlay(overlay, output_path)
                    logger.info(f"Overlay sauvegardé: {output_path}")

                except Exception as e:
                    logger.error(f"Erreur lors du traitement de {img_name}: {str(e)}")
                    continue

        logger.info("[SUCCESS] Post-traitement personnalisé terminé")
        return True

    except Exception as e:
        logger.error(f"[ERROR] Erreur lors du post-traitement personnalisé: {e}")
        return False

def main():
    """Fonction principale d'inférence"""
    start_time = time.time()

    try:
        logger.info("[START] Début de l'inférence nnUNet")

        # 0. Configuration du dossier de sortie avec versionnage
        setup_output_folder()

        logger.info(f"[CONFIG] Configuration:")
        logger.info(f"   - Dataset ID: {DATASET_ID}")
        logger.info(f"   - Configuration: {CONFIGURATION}")
        logger.info(f"   - Époques: {EPOCHS}")
        logger.info(f"   - Trainer: nnUNetTrainer_{EPOCHS}epochs")
        logger.info(f"   - GPU: {GPU_ID}")
        logger.info(f"   - Input: {INPUT_FOLDER}")
        logger.info(f"   - Output: {OUTPUT_FOLDER}")

        # 1. Vérifications préliminaires
        if not check_model_exists():
            return False

        if not check_input_folder():
            return False

        # 2. Prétraitement des images d'entrée
        if not preprocess_input_images():
            return False

        # 3. Exécuter l'inférence
        success = run_inference()

        if not success:
            logger.error("[ERROR] Échec de l'inférence")
            return False

        # Temps total
        total_time = time.time() - start_time
        logger.info(f"[COMPLETE] Inférence terminée en {total_time/60:.2f} minutes")

        return True

    except Exception as e:
        logger.error(f"[FATAL] Erreur fatale: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
